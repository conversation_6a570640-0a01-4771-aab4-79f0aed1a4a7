/**
 * 权限管理 Composable
 * 前端权限控制和菜单权限管理
 */

import { computed } from "vue";
import { useUserStore } from "@/stores/user";

// 权限常量定义
export const PERMISSIONS = {
  // 合同权限
  CONTRACT_CREATE: "contract:create",
  CONTRACT_READ: "contract:read",
  CONTRACT_UPDATE: "contract:update",
  CONTRACT_DELETE: "contract:delete",
  CONTRACT_REVIEW: "contract:review",
  CONTRACT_ASSIGN_NUMBER: "contract:assign_number",
  CONTRACT_VIEW_PENDING_NUMBER: "contract:view_pending_number",
  CONTRACT_MANAGE_NUMBER: "contract:manage_number",

  // 用户权限
  USER_CREATE: "user:create",
  USER_READ: "user:read",
  USER_UPDATE: "user:update",
  USER_DELETE: "user:delete",
  USER_MANAGE: "user:manage",

  // 系统权限
  SYSTEM_STATS: "system:stats",
  SYSTEM_MANAGE: "system:manage",
};

// 角色权限映射
export const ROLE_PERMISSIONS = {
  employee: [
    PERMISSIONS.CONTRACT_CREATE,
    PERMISSIONS.CONTRACT_READ,
    PERMISSIONS.CONTRACT_UPDATE,
    PERMISSIONS.CONTRACT_DELETE, // 员工可以删除自己的待审核合同
  ],
  reviewer: [PERMISSIONS.CONTRACT_READ, PERMISSIONS.CONTRACT_REVIEW],
  county_reviewer: [PERMISSIONS.CONTRACT_READ, PERMISSIONS.CONTRACT_REVIEW],
  city_reviewer: [PERMISSIONS.CONTRACT_READ, PERMISSIONS.CONTRACT_REVIEW],
  legal_officer: [
    PERMISSIONS.CONTRACT_READ,
    PERMISSIONS.CONTRACT_VIEW_PENDING_NUMBER,
    PERMISSIONS.CONTRACT_ASSIGN_NUMBER,
    PERMISSIONS.CONTRACT_MANAGE_NUMBER,
  ],
  admin: [
    PERMISSIONS.CONTRACT_CREATE,
    PERMISSIONS.CONTRACT_READ,
    PERMISSIONS.CONTRACT_UPDATE,
    PERMISSIONS.CONTRACT_DELETE,
    PERMISSIONS.CONTRACT_REVIEW,
    PERMISSIONS.CONTRACT_ASSIGN_NUMBER,
    PERMISSIONS.CONTRACT_VIEW_PENDING_NUMBER,
    PERMISSIONS.CONTRACT_MANAGE_NUMBER,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.SYSTEM_STATS,
    PERMISSIONS.SYSTEM_MANAGE,
  ],
};

// 菜单配置
export const MENU_CONFIG = {
  employee: [
    {
      key: "home",
      title: "首页",
      icon: "House",
      path: "/dashboard",
      component: "HomePage",
    },
    {
      key: "submit",
      title: "提交合同",
      icon: "Upload",
      path: "/submit",
      component: "SubmitPage",
    },
    {
      key: "my-contracts",
      title: "我的合同",
      icon: "Document",
      path: "/my-contracts",
      component: "MyContractsPage",
    },
    {
      key: "statistics",
      title: "我的统计",
      icon: "DataAnalysis",
      path: "/statistics",
      component: "StatisticsPage",
    },
    {
      key: "profile",
      title: "个人资料",
      icon: "User",
      path: "/profile",
      component: "ProfilePage",
    },
  ],
  county_reviewer: [
    {
      key: "home",
      title: "首页",
      icon: "House",
      path: "/dashboard",
      component: "HomePage",
    },
    {
      key: "county-reviewer-contracts",
      title: "我的审核任务",
      icon: "FolderOpened",
      path: "/county-reviewer-contracts",
      component: "CountyReviewerContractsPage",
    },
    {
      key: "statistics",
      title: "审核统计",
      icon: "DataAnalysis",
      path: "/statistics",
      component: "StatisticsPage",
    },
    {
      key: "profile",
      title: "个人资料",
      icon: "User",
      path: "/profile",
      component: "ProfilePage",
    },
  ],
  city_reviewer: [
    {
      key: "home",
      title: "首页",
      icon: "House",
      path: "/dashboard",
      component: "HomePage",
    },
    {
      key: "city-reviewer-contracts",
      title: "我的审核任务",
      icon: "FolderOpened",
      path: "/city-reviewer-contracts",
      component: "CityReviewerContractsPage",
    },
    {
      key: "statistics",
      title: "审核统计",
      icon: "DataAnalysis",
      path: "/statistics",
      component: "StatisticsPage",
    },
    {
      key: "profile",
      title: "个人资料",
      icon: "User",
      path: "/profile",
      component: "ProfilePage",
    },
  ],
  county_reviewer: [
    {
      key: "home",
      title: "首页",
      icon: "House",
      path: "/dashboard",
      component: "HomePage",
    },
    {
      key: "review-management",
      title: "审核管理",
      icon: "FolderOpened",
      path: "/review-management",
      component: "ReviewManagePage",
    },
    {
      key: "statistics",
      title: "审核统计",
      icon: "DataAnalysis",
      path: "/statistics",
      component: "StatisticsPage",
    },
    {
      key: "profile",
      title: "个人资料",
      icon: "User",
      path: "/profile",
      component: "ProfilePage",
    },
  ],
  city_reviewer: [
    {
      key: "home",
      title: "首页",
      icon: "House",
      path: "/dashboard",
      component: "HomePage",
    },
    {
      key: "review-management",
      title: "审核管理",
      icon: "FolderOpened",
      path: "/review-management",
      component: "ReviewManagePage",
    },
    {
      key: "statistics",
      title: "审核统计",
      icon: "DataAnalysis",
      path: "/statistics",
      component: "StatisticsPage",
    },
    {
      key: "profile",
      title: "个人资料",
      icon: "User",
      path: "/profile",
      component: "ProfilePage",
    },
  ],
  legal_officer: [
    {
      key: "home",
      title: "首页",
      icon: "House",
      path: "/dashboard",
      component: "HomePage",
    },
    {
      key: "legal-officer-contracts",
      title: "合同编号管理",
      icon: "Document",
      path: "/legal-officer-contracts",
      component: "LegalOfficerContractsPage",
    },
    {
      key: "profile",
      title: "个人资料",
      icon: "User",
      path: "/profile",
      component: "ProfilePage",
    },
  ],
  reviewer: [
    {
      key: "home",
      title: "首页",
      icon: "House",
      path: "/dashboard",
      component: "HomePage",
    },
    {
      key: "review-management",
      title: "审核管理",
      icon: "FolderOpened",
      path: "/review-management",
      component: "ReviewManagePage",
    },
    {
      key: "statistics",
      title: "审核统计",
      icon: "DataAnalysis",
      path: "/statistics",
      component: "StatisticsPage",
    },
    {
      key: "profile",
      title: "个人资料",
      icon: "User",
      path: "/profile",
      component: "ProfilePage",
    },
  ],
  admin: [
    {
      key: "home",
      title: "首页",
      icon: "House",
      path: "/dashboard",
      component: "HomePage",
    },
    {
      key: "user-management",
      title: "用户管理",
      icon: "UserFilled",
      path: "/user-management",
      component: "UserManagePage",
    },
    {
      key: "admin-contracts",
      title: "合同管理中心",
      icon: "FolderOpened",
      path: "/admin-contracts",
      component: "AdminContractsPage",
    },
    {
      key: "system-stats",
      title: "系统统计",
      icon: "DataBoard",
      path: "/system-stats",
      component: "SystemManagePage",
    },
    {
      key: "settings",
      title: "系统设置",
      icon: "Setting",
      path: "/settings",
      component: "SettingsPage",
    },
    {
      key: "profile",
      title: "个人资料",
      icon: "User",
      path: "/profile",
      component: "ProfilePage",
    },
  ],
};

/**
 * 权限管理 Hook
 */
export function usePermission() {
  const userStore = useUserStore();
  const user = computed(() => userStore.user);
  const userRole = computed(() => userStore.userRole);
  const isAuthenticated = computed(() => userStore.isAuthenticated);

  /**
   * 获取用户权限列表
   */
  const userPermissions = computed(() => {
    if (!isAuthenticated.value || !userRole.value) {
      return [];
    }
    return ROLE_PERMISSIONS[userRole.value] || [];
  });

  /**
   * 检查是否具有指定权限
   * @param {string|Array} permissions - 权限或权限数组
   * @returns {boolean} 是否具有权限
   */
  const hasPermission = (permissions) => {
    if (!isAuthenticated.value) {
      return false;
    }

    const userPerms = userPermissions.value;

    if (typeof permissions === "string") {
      return userPerms.includes(permissions);
    }

    if (Array.isArray(permissions)) {
      return permissions.every((permission) => userPerms.includes(permission));
    }

    return false;
  };

  /**
   * 检查是否具有任一权限
   * @param {Array} permissions - 权限数组
   * @returns {boolean} 是否具有任一权限
   */
  const hasAnyPermission = (permissions) => {
    if (!isAuthenticated.value || !Array.isArray(permissions)) {
      return false;
    }

    const userPerms = userPermissions.value;
    return permissions.some((permission) => userPerms.includes(permission));
  };

  /**
   * 检查是否具有指定角色
   * @param {string|Array} roles - 角色或角色数组
   * @returns {boolean} 是否具有角色
   */
  const hasRole = (roles) => {
    if (!isAuthenticated.value || !userRole.value) {
      return false;
    }

    if (typeof roles === "string") {
      return userRole.value === roles;
    }

    if (Array.isArray(roles)) {
      return roles.includes(userRole.value);
    }

    return false;
  };

  /**
   * 获取用户菜单配置
   */
  const userMenus = computed(() => {
    if (!isAuthenticated.value || !userRole.value) {
      return [];
    }
    return MENU_CONFIG[userRole.value] || [];
  });

  /**
   * 检查菜单项是否可见
   * @param {Object} menuItem - 菜单项
   * @returns {boolean} 是否可见
   */
  const isMenuVisible = (menuItem) => {
    if (!menuItem.permission) {
      return true;
    }
    return hasPermission(menuItem.permission);
  };

  /**
   * 过滤可见菜单
   */
  const visibleMenus = computed(() => {
    return userMenus.value.filter((menu) => isMenuVisible(menu));
  });

  /**
   * 检查是否可以访问路由
   * @param {string} path - 路由路径
   * @returns {boolean} 是否可以访问
   */
  const canAccessRoute = (path) => {
    if (!isAuthenticated.value) {
      return false;
    }

    // 查找对应的菜单项
    const menuItem = userMenus.value.find((menu) => menu.path === path);
    if (!menuItem) {
      return false;
    }

    return isMenuVisible(menuItem);
  };

  /**
   * 检查是否可以修改合同
   * @param {Object} contract - 合同对象
   * @returns {boolean} 是否可以修改
   */
  const canModifyContract = (contract) => {
    if (!isAuthenticated.value || !user.value) {
      return false;
    }

    // 管理员不能修改合同内容
    if (userRole.value === "admin") {
      return false;
    }

    // 只有提交人可以修改
    if (contract.submitter_id !== user.value.id) {
      return false;
    }

    // 员工可以修改 pending 或 rejected 状态的合同
    return ["pending", "rejected"].includes(contract.status);
  };

  /**
   * 检查是否可以审核合同
   * @param {Object} contract - 合同对象
   * @returns {boolean} 是否可以审核
   */
  const canReviewContract = (contract) => {
    if (!isAuthenticated.value || !user.value) {
      return false;
    }

    // 只有待审核状态的合同可以审核（支持县局和市局审核）
    const reviewableStatuses = ["pending", "pending_city_review"];
    if (!reviewableStatuses.includes(contract.status)) {
      return false;
    }

    // 不能审核自己提交的合同
    if (contract.submitter_id === user.value.id) {
      return false;
    }

    // 管理员可以审核任何合同
    if (userRole.value === "admin") {
      return true;
    }

    // 审核员只能审核分配给自己的合同
    if (
      (userRole.value === "reviewer" ||
        userRole.value === "county_reviewer" ||
        userRole.value === "city_reviewer") &&
      contract.reviewer_id === user.value.id
    ) {
      // 额外检查：市局审核员只能审核待市局审核的合同
      if (
        userRole.value === "city_reviewer" &&
        contract.status !== "pending_city_review"
      ) {
        return false;
      }
      // 县局审核员只能审核待审核的合同
      if (
        (userRole.value === "county_reviewer" ||
          userRole.value === "reviewer") &&
        contract.status !== "pending"
      ) {
        return false;
      }
      return true;
    }

    return false;
  };

  /**
   * 检查是否可以查看合同详情
   * @param {Object} contract - 合同对象
   * @returns {boolean} 是否可以查看
   */
  const canViewContract = (contract) => {
    if (!isAuthenticated.value || !user.value) {
      return false;
    }

    // 管理员可以查看所有合同
    if (userRole.value === "admin") {
      return true;
    }

    // 提交人可以查看自己的合同
    if (contract.submitter_id === user.value.id) {
      return true;
    }

    // 审核员可以查看分配给自己的合同
    if (
      ["reviewer", "county_reviewer", "city_reviewer"].includes(
        userRole.value,
      ) &&
      contract.reviewer_id === user.value.id
    ) {
      return true;
    }

    // 法规员可以查看已通过审核的合同（用于分配合同编号）
    if (userRole.value === "legal_officer") {
      // 法规员可以查看待分配编号和已完成的合同
      return ["pending_contract_number", "completed"].includes(contract.status);
    }

    return false;
  };

  return {
    // 权限相关
    userPermissions,
    hasPermission,
    hasAnyPermission,
    hasRole,

    // 菜单相关
    userMenus,
    visibleMenus,
    isMenuVisible,
    canAccessRoute,

    // 业务权限
    canModifyContract,
    canReviewContract,
    canViewContract,

    // 常量
    PERMISSIONS,
    ROLE_PERMISSIONS,
  };
}
